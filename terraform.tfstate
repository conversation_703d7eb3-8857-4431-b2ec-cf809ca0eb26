{"version": 4, "terraform_version": "1.5.7", "serial": 13, "lineage": "0c23762c-0eda-90fd-3f42-2c65ff410aaf", "outputs": {"default_firewall_rule_ids": {"value": {"allow_http_https": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-http-https", "allow_internal": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-internal", "allow_ssh": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-ssh"}, "type": ["object", {"allow_http_https": "string", "allow_internal": "string", "allow_ssh": "string"}]}, "firewall_rule_ids": {"value": {"allow-app-to-db": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/allow-app-to-db", "allow-load-balancer": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/allow-load-balancer"}, "type": ["object", {"allow-app-to-db": "string", "allow-load-balancer": "string"}]}, "nat_gateway_ids": {"value": {"us-central1": "p-dev-bt-concept-ywqb-1/us-central1/main-vpc-router/main-vpc-nat"}, "type": ["object", {"us-central1": "string"}]}, "network_info": {"value": {"network_id": "projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "network_name": "main-vpc", "network_self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "subnets": {"app-subnet": {"cidr_range": "********/24", "gateway_address": "********", "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/app-subnet", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/app-subnet"}, "db-subnet": {"cidr_range": "********/24", "gateway_address": "********", "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/db-subnet", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/db-subnet"}, "gke-subnet": {"cidr_range": "********/24", "gateway_address": "********", "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/gke-subnet", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/gke-subnet"}, "web-subnet": {"cidr_range": "********/24", "gateway_address": "********", "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/web-subnet", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/web-subnet"}}}, "type": ["object", {"network_id": "string", "network_name": "string", "network_self_link": "string", "subnets": ["object", {"app-subnet": ["object", {"cidr_range": "string", "gateway_address": "string", "id": "string", "region": "string", "self_link": "string"}], "db-subnet": ["object", {"cidr_range": "string", "gateway_address": "string", "id": "string", "region": "string", "self_link": "string"}], "gke-subnet": ["object", {"cidr_range": "string", "gateway_address": "string", "id": "string", "region": "string", "self_link": "string"}], "web-subnet": ["object", {"cidr_range": "string", "gateway_address": "string", "id": "string", "region": "string", "self_link": "string"}]}]}]}, "router_ids": {"value": {"us-central1": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/routers/main-vpc-router"}, "type": ["object", {"us-central1": "string"}]}, "subnet_cidr_ranges": {"value": {"app-subnet": "********/24", "db-subnet": "********/24", "gke-subnet": "********/24", "web-subnet": "********/24"}, "type": ["object", {"app-subnet": "string", "db-subnet": "string", "gke-subnet": "string", "web-subnet": "string"}]}, "subnet_gateway_addresses": {"value": {"app-subnet": "********", "db-subnet": "********", "gke-subnet": "********", "web-subnet": "********"}, "type": ["object", {"app-subnet": "string", "db-subnet": "string", "gke-subnet": "string", "web-subnet": "string"}]}, "subnet_ids": {"value": {"app-subnet": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/app-subnet", "db-subnet": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/db-subnet", "gke-subnet": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/gke-subnet", "web-subnet": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/web-subnet"}, "type": ["object", {"app-subnet": "string", "db-subnet": "string", "gke-subnet": "string", "web-subnet": "string"}]}, "subnet_self_links": {"value": {"app-subnet": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/app-subnet", "db-subnet": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/db-subnet", "gke-subnet": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/gke-subnet", "web-subnet": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/web-subnet"}, "type": ["object", {"app-subnet": "string", "db-subnet": "string", "gke-subnet": "string", "web-subnet": "string"}]}, "vpc_network_id": {"value": "projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "type": "string"}, "vpc_network_name": {"value": "main-vpc", "type": "string"}, "vpc_network_self_link": {"value": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "type": "string"}}, "resources": [{"mode": "managed", "type": "google_compute_firewall", "name": "allow_http_https", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"allow": [{"ports": ["80", "443"], "protocol": "tcp"}], "creation_timestamp": "2025-08-27T07:16:35.696-07:00", "deny": [], "description": "Allow HTTP and HTTPS traffic", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-http-https", "log_config": [], "name": "main-vpc-allow-http-https", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "priority": 1000, "project": "p-dev-bt-concept-ywqb-1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-http-https", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": null, "source_tags": null, "target_service_accounts": null, "target_tags": ["http-server", "https-server"], "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}]}, {"mode": "managed", "type": "google_compute_firewall", "name": "allow_internal", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"allow": [{"ports": ["0-65535"], "protocol": "tcp"}, {"ports": ["0-65535"], "protocol": "udp"}, {"ports": [], "protocol": "icmp"}], "creation_timestamp": "2025-08-27T07:16:35.709-07:00", "deny": [], "description": "Allow internal communication within VPC", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-internal", "log_config": [], "name": "main-vpc-allow-internal", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "priority": 1000, "project": "p-dev-bt-concept-ywqb-1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-internal", "source_ranges": ["********/24", "********/24", "********/24", "********/24"], "source_service_accounts": null, "source_tags": null, "target_service_accounts": null, "target_tags": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}]}, {"mode": "managed", "type": "google_compute_firewall", "name": "allow_ssh", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"allow": [{"ports": ["22"], "protocol": "tcp"}], "creation_timestamp": "2025-08-27T07:16:36.638-07:00", "deny": [], "description": "Allow SSH access", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-ssh", "log_config": [], "name": "main-vpc-allow-ssh", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "priority": 1000, "project": "p-dev-bt-concept-ywqb-1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/firewalls/main-vpc-allow-ssh", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": null, "source_tags": null, "target_service_accounts": null, "target_tags": ["ssh-allowed"], "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}]}, {"mode": "managed", "type": "google_compute_firewall", "name": "firewall_rules", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "allow-app-to-db", "schema_version": 1, "attributes": {"allow": [{"ports": ["3306", "5432"], "protocol": "tcp"}], "creation_timestamp": "2025-08-27T07:16:36.691-07:00", "deny": [], "description": "Allow app servers to access databases", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/allow-app-to-db", "log_config": [], "name": "allow-app-to-db", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "priority": 1000, "project": "p-dev-bt-concept-ywqb-1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/firewalls/allow-app-to-db", "source_ranges": ["********/24"], "source_service_accounts": null, "source_tags": null, "target_service_accounts": null, "target_tags": ["database"], "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}, {"index_key": "allow-load-balancer", "schema_version": 1, "attributes": {"allow": [{"ports": ["80", "443", "8080"], "protocol": "tcp"}], "creation_timestamp": "2025-08-27T07:16:35.612-07:00", "deny": [], "description": "Allow Google Load Balancer health checks", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/p-dev-bt-concept-ywqb-1/global/firewalls/allow-load-balancer", "log_config": [], "name": "allow-load-balancer", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "priority": 1000, "project": "p-dev-bt-concept-ywqb-1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/firewalls/allow-load-balancer", "source_ranges": ["***********/22", "**********/16"], "source_service_accounts": null, "source_tags": null, "target_service_accounts": null, "target_tags": ["web-server"], "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}]}, {"mode": "managed", "type": "google_compute_network", "name": "vpc_network", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "VPC network created by Terraform", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "internal_ipv6_range": "", "mtu": 1460, "name": "main-vpc", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "*****************", "project": "p-dev-bt-concept-ywqb-1", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "google_compute_router", "name": "router", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "us-central1", "schema_version": 0, "attributes": {"bgp": [{"advertise_mode": "DEFAULT", "advertised_groups": null, "advertised_ip_ranges": [], "asn": 64514, "identifier_range": "", "keepalive_interval": 20}], "creation_timestamp": "2025-08-27T07:16:37.698-07:00", "description": "", "encrypted_interconnect_router": false, "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/routers/main-vpc-router", "name": "main-vpc-router", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "project": "p-dev-bt-concept-ywqb-1", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/routers/main-vpc-router", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}]}, {"mode": "managed", "type": "google_compute_router_nat", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "us-central1", "schema_version": 0, "attributes": {"auto_network_tier": "PREMIUM", "drain_nat_ips": null, "enable_dynamic_port_allocation": false, "enable_endpoint_independent_mapping": false, "endpoint_types": ["ENDPOINT_TYPE_VM"], "icmp_idle_timeout_sec": 30, "id": "p-dev-bt-concept-ywqb-1/us-central1/main-vpc-router/main-vpc-nat", "log_config": [{"enable": true, "filter": "ERRORS_ONLY"}], "max_ports_per_vm": 0, "min_ports_per_vm": 0, "name": "main-vpc-nat", "nat_ip_allocate_option": "AUTO_ONLY", "nat_ips": null, "project": "p-dev-bt-concept-ywqb-1", "region": "us-central1", "router": "main-vpc-router", "rules": [], "source_subnetwork_ip_ranges_to_nat": "ALL_SUBNETWORKS_ALL_IP_RANGES", "subnetwork": [], "tcp_established_idle_timeout_sec": 1200, "tcp_time_wait_timeout_sec": 120, "tcp_transitory_idle_timeout_sec": 30, "timeouts": null, "udp_idle_timeout_sec": 30}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network", "google_compute_router.router"]}]}, {"mode": "managed", "type": "google_compute_subnetwork", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "app-subnet", "schema_version": 0, "attributes": {"creation_timestamp": "2025-08-27T07:16:41.736-07:00", "description": "Private subnet for application servers", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "********", "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/app-subnet", "internal_ipv6_prefix": "", "ip_cidr_range": "********/24", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [{"aggregation_interval": "INTERVAL_10_MIN", "filter_expr": "true", "flow_sampling": 0.5, "metadata": "INCLUDE_ALL_METADATA", "metadata_fields": null}], "name": "app-subnet", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "private_ip_google_access": true, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "p-dev-bt-concept-ywqb-1", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [], "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/app-subnet", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}, {"index_key": "db-subnet", "schema_version": 0, "attributes": {"creation_timestamp": "2025-08-27T07:16:40.924-07:00", "description": "Private subnet for databases", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "********", "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/db-subnet", "internal_ipv6_prefix": "", "ip_cidr_range": "********/24", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [{"aggregation_interval": "INTERVAL_10_MIN", "filter_expr": "true", "flow_sampling": 0.5, "metadata": "INCLUDE_ALL_METADATA", "metadata_fields": null}], "name": "db-subnet", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "private_ip_google_access": true, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "p-dev-bt-concept-ywqb-1", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [], "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/db-subnet", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}, {"index_key": "gke-subnet", "schema_version": 0, "attributes": {"creation_timestamp": "2025-08-27T07:16:39.159-07:00", "description": "Subnet for GKE cluster", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "********", "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/gke-subnet", "internal_ipv6_prefix": "", "ip_cidr_range": "********/24", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [{"aggregation_interval": "INTERVAL_10_MIN", "filter_expr": "true", "flow_sampling": 0.5, "metadata": "INCLUDE_ALL_METADATA", "metadata_fields": null}], "name": "gke-subnet", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "private_ip_google_access": true, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "p-dev-bt-concept-ywqb-1", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "********/16", "range_name": "gke-pods"}, {"ip_cidr_range": "********/16", "range_name": "gke-services"}], "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/gke-subnet", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}, {"index_key": "web-subnet", "schema_version": 0, "attributes": {"creation_timestamp": "2025-08-27T07:16:40.570-07:00", "description": "Public subnet for web servers", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "********", "id": "projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/web-subnet", "internal_ipv6_prefix": "", "ip_cidr_range": "********/24", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [{"aggregation_interval": "INTERVAL_10_MIN", "filter_expr": "true", "flow_sampling": 0.5, "metadata": "INCLUDE_ALL_METADATA", "metadata_fields": null}], "name": "web-subnet", "network": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/global/networks/main-vpc", "private_ip_google_access": false, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "p-dev-bt-concept-ywqb-1", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [], "self_link": "https://www.googleapis.com/compute/v1/projects/p-dev-bt-concept-ywqb-1/regions/us-central1/subnetworks/web-subnet", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["google_compute_network.vpc_network"]}]}], "check_results": null}