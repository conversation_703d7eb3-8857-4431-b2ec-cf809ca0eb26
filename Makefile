# Makefile for GCP VPC Terraform automation

.PHONY: help init plan apply destroy validate format clean check-auth check-apis

# Default target
help:
	@echo "Available targets:"
	@echo "  init        - Initialize Terraform"
	@echo "  plan        - Create Terraform execution plan"
	@echo "  apply       - Apply Terraform configuration"
	@echo "  destroy     - Destroy Terraform-managed infrastructure"
	@echo "  validate    - Validate Terraform configuration"
	@echo "  format      - Format Terraform files"
	@echo "  clean       - Clean Terraform files"
	@echo "  check-auth  - Check GCP authentication"
	@echo "  check-apis  - Check required GCP APIs"
	@echo "  setup       - Complete setup (auth, APIs, init)"

# Check if terraform.tfvars exists
check-vars:
	@if [ ! -f terraform.tfvars ]; then \
		echo "Error: terraform.tfvars not found. Copy terraform.tfvars.example and customize it."; \
		echo "cp terraform.tfvars.example terraform.tfvars"; \
		exit 1; \
	fi

# Check GCP authentication
check-auth:
	@echo "Checking GCP authentication..."
	@gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -1 || \
		(echo "Error: No active GCP authentication found. Run 'gcloud auth login' or set GOOGLE_APPLICATION_CREDENTIALS" && exit 1)
	@echo "✓ GCP authentication is configured"

# Check required GCP APIs
check-apis:
	@echo "Checking required GCP APIs..."
	@PROJECT_ID=$$(gcloud config get-value project 2>/dev/null) && \
	if [ -z "$$PROJECT_ID" ]; then \
		echo "Error: No default GCP project set. Run 'gcloud config set project YOUR_PROJECT_ID'"; \
		exit 1; \
	fi && \
	echo "Checking APIs for project: $$PROJECT_ID" && \
	gcloud services list --enabled --filter="name:compute.googleapis.com" --format="value(name)" | grep -q compute.googleapis.com || \
		(echo "Enabling Compute Engine API..." && gcloud services enable compute.googleapis.com) && \
	gcloud services list --enabled --filter="name:servicenetworking.googleapis.com" --format="value(name)" | grep -q servicenetworking.googleapis.com || \
		(echo "Enabling Service Networking API..." && gcloud services enable servicenetworking.googleapis.com) && \
	echo "✓ Required APIs are enabled"

# Initialize Terraform
init: check-vars check-auth
	@echo "Initializing Terraform..."
	terraform init
	@echo "✓ Terraform initialized"

# Validate Terraform configuration
validate:
	@echo "Validating Terraform configuration..."
	terraform validate
	@echo "✓ Configuration is valid"

# Format Terraform files
format:
	@echo "Formatting Terraform files..."
	terraform fmt -recursive
	@echo "✓ Files formatted"

# Create Terraform plan
plan: check-vars validate
	@echo "Creating Terraform execution plan..."
	terraform plan -out=tfplan
	@echo "✓ Plan created (saved as tfplan)"

# Apply Terraform configuration
apply: check-vars
	@echo "Applying Terraform configuration..."
	@if [ -f tfplan ]; then \
		terraform apply tfplan; \
	else \
		terraform apply; \
	fi
	@echo "✓ Configuration applied"

# Destroy infrastructure
destroy: check-vars
	@echo "WARNING: This will destroy all Terraform-managed infrastructure!"
	@read -p "Are you sure? Type 'yes' to continue: " confirm && [ "$$confirm" = "yes" ] || exit 1
	terraform destroy
	@echo "✓ Infrastructure destroyed"

# Clean Terraform files
clean:
	@echo "Cleaning Terraform files..."
	rm -f tfplan
	rm -f terraform.tfstate.backup
	rm -rf .terraform/
	@echo "✓ Terraform files cleaned"

# Complete setup
setup: check-auth check-apis init validate
	@echo "✓ Setup complete! You can now run 'make plan' and 'make apply'"

# Show Terraform outputs
output:
	@terraform output

# Show current state
show:
	@terraform show

# Import existing resources (example)
import-example:
	@echo "Example import commands:"
	@echo "terraform import google_compute_network.vpc_network projects/PROJECT_ID/global/networks/NETWORK_NAME"
	@echo "terraform import google_compute_subnetwork.subnets[\"SUBNET_NAME\"] projects/PROJECT_ID/regions/REGION/subnetworks/SUBNET_NAME"
